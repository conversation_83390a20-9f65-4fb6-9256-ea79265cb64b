﻿CREATE PROCEDURE [Log].[SP_PowerBiRefreshLogStart]
    @RunId 					NVARCHAR(MAX),
	@DatasetId				NVARCHAR(MAX),
	@WorkspaceId			NVARCHAR(MAX),
	@Status					NVARCHAR(MAX),
	@GroupId				NVARCHAR(MAX),
	@TriggerName			NVARCHAR(MAX),
	@Stream					NVARCHAR(MAX)
AS
BEGIN
    -- Insert a new record into the IngestionFrameworkChildLog table
    INSERT INTO [Log].[PowerBiRefreshLog] (RunId,StartTime,EndTime,DatasetId,WorkspaceId,[Status],MessageError,GroupId,TriggerName,[Stream])
    VALUES (@RunId,CURRENT_TIMESTAMP,NULL,@DatasetId,@WorkspaceId,@Status, NULL, @GroupId, @TriggerName, @Stream);
END;