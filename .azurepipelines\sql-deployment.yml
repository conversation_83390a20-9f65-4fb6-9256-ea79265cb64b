name: SQL Database CI/CD Pipeline

variables:
  - name: applicationCode
    value: "DATA-PLATFORM"
  - group: ${{ variables.applicationCode }}-INFRA-${{ parameters.Environment }}
  - ${{ if ne(parameters.Environment, 'DEV') }}:
    - group: ${{ variables.applicationCode }}-APP-${{ parameters.Environment }}

parameters:
  - name: RunMode
    displayName: "Pipeline Run Mode"
    type: string
    values:
      - CD_Only
    default: CD_Only
  - name: Environment
    displayName: "Please select the environment to deploy resources to (CD only)"
    type: string
    values:
      - DEV
      - UAT
      - PROD
    default: DEV
  - name: DeployVersion
    displayName: "Version to deploy (CD only - e.g., 1.0.0, 1.1.0, etc.)"
    type: string
    default: "1.0.0"
  - name: DebugMode
    displayName: "Enable debug logging"
    type: boolean
    default: false

# CI runs automatically on main branch pushes
# CD runs only when manually triggered with CD_Only mode
trigger:
  branches:
    include:
      - main

# Disable manual triggers for CI - CI should only run automatically
pr: none

pool:
  name: $(AGENTPOOL)

stages:
# CI Stage - runs automatically on push to main branch only
- ${{ if ne(variables['Build.Reason'], 'Manual') }}:
  - stage: 'CI'
    displayName: 'Continuous Integration'
    jobs:
    - job: "CI_Job"
      displayName: "CI Job"
      steps:
      - task: PowerShell@2
        displayName: 'Pipeline Debug Information'
        condition: eq('${{ parameters.DebugMode }}', 'true')
        inputs:
          targetType: 'inline'
          script: |
            Write-Host "##[section]Pipeline Debug Information"
            Write-Host "Build.Reason: $(Build.Reason)"
            Write-Host "Build.SourceBranch: $(Build.SourceBranch)"
            Write-Host "Build.BuildNumber: $(Build.BuildNumber)"
            Write-Host "Build.BuildId: $(Build.BuildId)"
            Write-Host "System.TeamProject: $(System.TeamProject)"
            Write-Host "Agent.Name: $(Agent.Name)"
            Write-Host "Agent.OS: $(Agent.OS)"
            Write-Host "Environment: ${{ parameters.Environment }}"
            Write-Host "DebugMode: ${{ parameters.DebugMode }}"
            Write-Host "Pipeline Type: Automatic CI (triggered by push to main)"

            Write-Host "##[section]Environment Variables"
            Get-ChildItem Env: | Where-Object { $_.Name -like "*SERVICE*" -or $_.Name -like "*SQL*" -or $_.Name -like "*ARTIFACT*" } | Sort-Object Name | ForEach-Object {
              Write-Host "$($_.Name): $($_.Value)"
            }

      - checkout: self
        fetchTags: true
        fetchDepth: 0   # Fetch full history for proper tag comparison
        persistCredentials: true
        displayName: 'Checkout with tags'

      - task: MSBuild@1
        displayName: 'Build / Generate DACPAC'
        inputs:
          solution: '**/*.sqlproj'
          msbuildArguments: '/p:OutDir=$(Pipeline.Workspace)/sqldacpac/ /p:Configuration=Release'
          platform: 'Any CPU'

      - task: SqlAzureDacpacDeployment@1
        displayName: 'Deploy Report'
        inputs:
          azureSubscription: "$(SERVICE_CONNECTION)"
          ServerName: $(SQL_SERVER_FQDN)
          DatabaseName: $(SQL_DB_NAME)
          AuthenticationType: servicePrincipal
          DeploymentAction: 'DeployReport'
          DacpacFile: '$(Pipeline.Workspace)/sqldacpac/ADFControlTablesDB.dacpac'
          SqlAdditionalArguments: '-b'

      - task: CopyFiles@2
        displayName: 'Copy DACPAC to Staging Directory'
        inputs:
          SourceFolder: '$(Pipeline.Workspace)/sqldacpac/'
          Contents: '**'
          TargetFolder: '$(Build.ArtifactStagingDirectory)/dacpac'
          CleanTargetFolder: true

      - task: PublishBuildArtifacts@1
        displayName: 'Publish DACPAC artifact'
        inputs:
          PathtoPublish: '$(Pipeline.Workspace)/sqldacpac/'
          ArtifactName: 'sqldacpac'

      - task: PowerShell@2
        displayName: "Generate Version and Tag"
        name: SetVersion
        inputs:
          targetType: 'inline'
          script: |
            # Deleting local remote-tracking branches and tags that have been removed from the remote.
            git fetch --prune --prune-tags

            # Get the latest tag (if any)
            $lastTag = git describe --tags --abbrev=0 --always

            # Find changes in ADFControlTablesDB/ since the last tag
            $changes = git diff --name-only $lastTag HEAD -- ADFControlTablesDB/
            Write-Host "Changes detected: $changes"

            if ([string]::IsNullOrWhiteSpace($changes)) {
                Write-Host "No changes detected in ADFControlTablesDB folder. Skipping version increment."

                # Use existing tag or default version without increment
                $existingTag = $lastTag
                if (-not $existingTag) {
                    $existingTag = "v1.0.0"
                }

                Write-Host "##vso[task.setvariable variable=TAG_VERSION;isOutput=true]$existingTag"
                Write-Host "##vso[task.setvariable variable=shouldPublish;isOutput=true]false"

                Write-Host "Existing tag: $existingTag"
                Write-Host "Should publish: false"
                exit 0
            }

            Write-Host "##vso[task.setvariable variable=shouldPublish;isOutput=true]true"

            # Get latest tag matching 'v*' pattern, sorted by version
            $latestTag = git tag --list "v*" --sort=-v:refname | Select-Object -First 1
            Write-Output "Found tag: $($latestTag -or 'NONE')"

            Write-Output "Latest tag found: $latestTag"

            if (-not ($latestTag -match '^v\d+\.\d+$')) {
                Write-Host "No valid or Incorrect tag format found. Using v1.0"
                $version = [pscustomobject]@{ Major = 1; Minor = 0 }
            } else {
                $latestTag = $latestTag.Trim()
                $versionParts = $latestTag.TrimStart('v') -split '\.'
                $major = [int]$versionParts[0]
                $minor = [int]$versionParts[1]

                # Increment minor version
                $minor += 1

                # If minor reaches 100, increment major and reset minor
                if ($minor -eq 100) {
                    $major += 1
                    $minor = 0
                }

                $version = [pscustomobject]@{
                    Major = $major
                    Minor = $minor
                }
            }

            $newTag = "v$($version.Major).$($version.Minor)"
            $packageVersion = "$($version.Major).$($version.Minor).0"
            Write-Host "##[section]New Tag: $newTag"
            Write-Host "##[section]Package Version: $packageVersion"

            # Set the tag and package version as output variables
            Write-Host "##vso[task.setvariable variable=TAG_VERSION;isOutput=true]$newTag"
            Write-Host "##vso[task.setvariable variable=PACKAGE_VERSION;isOutput=true]$packageVersion"

            # Create and push tag
            git tag -a $newTag -m "Release $newTag"
            git push origin $newTag
        env:
          SYSTEM_ACCESSTOKEN: $(System.AccessToken)

      - task: PowerShell@2
        displayName: 'Create Package Manifest'
        condition: and(succeeded(), eq(variables['SetVersion.shouldPublish'], 'true'))
        inputs:
          targetType: 'inline'
          script: |
            $version = "$(SetVersion.PACKAGE_VERSION)"
            $buildNumber = "$(Build.BuildNumber)"
            $sourceVersion = "$(Build.SourceVersion)"
            $buildDate = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"

            $manifest = @{
              name = "gd-sql-obsd"
              version = $version
              description = "SQL Database Control Tables Package"
              buildNumber = $buildNumber
              sourceCommit = $sourceVersion
              buildDate = $buildDate
              contents = @(
                @{
                  path = "dacpac"
                  description = "SQL Server Data-tier Application Package"
                  type = "dacpac"
                }
              )
            }

            $manifestJson = $manifest | ConvertTo-Json -Depth 3
            $manifestPath = "$(Build.ArtifactStagingDirectory)/package-manifest.json"
            $manifestJson | Out-File -FilePath $manifestPath -Encoding utf8

            Write-Host "Package manifest created at: $manifestPath"
            Write-Host "Manifest content:"
            Write-Host $manifestJson

      - task: UniversalPackages@0
        displayName: 'Publish to Universal Package Feed'
        condition: and(succeeded(), eq(variables['SetVersion.shouldPublish'], 'true'))
        retryCountOnTaskFailure: 3
        inputs:
          command: 'publish'
          publishDirectory: '$(Build.ArtifactStagingDirectory)'
          feedsToUsePublish: 'internal'
          vstsFeedPublish: 'GovDigital/$(ARTIFACT_FEED)'
          vstsFeedPackagePublish: 'gd-sql-obsd'
          versionOption: 'custom'
          versionPublish: '$(SetVersion.PACKAGE_VERSION)'
          packagePublishDescription: 'SQL Database Control Tables - Version $(SetVersion.PACKAGE_VERSION)'
        env:
          ARTIFACT_FEED: $(ARTIFACT_FEED)

      - task: PowerShell@2
        displayName: 'Update Pipeline MetaData'
        condition: and(succeeded(), eq(variables['SetVersion.shouldPublish'], 'true'))
        inputs:
          targetType: 'inline'
          script: |
            Write-Host "##vso[build.updatebuildnumber]Build-$(Build.BuildId) Version: $(SetVersion.PACKAGE_VERSION)"


# CD Stage - runs only when manually triggered with CD_Only mode
- ${{ if eq(variables['Build.Reason'], 'Manual') }}:

  - stage: 'CD'
    displayName: 'Continuous Deployment'
    jobs:
    - deployment: "Deploy_to_SQLServer"
      displayName: 'Deploy to SQL Server'
      timeoutInMinutes: 0
      environment: ${{ parameters.Environment }}
      strategy:
        runOnce:
          deploy:
            steps:
            - task: PowerShell@2
              displayName: 'CD Debug Information'
              condition: eq('${{ parameters.DebugMode }}', 'true')
              inputs:
                targetType: 'inline'
                script: |
                  Write-Host "##[section]CD Pipeline Debug Information"
                  Write-Host "Environment: ${{ parameters.Environment }}"
                  Write-Host "Deploy Version: ${{ parameters.DeployVersion }}"
                  Write-Host "Build.Reason: $(Build.Reason)"
                  Write-Host "Pipeline Type: Manual CD (Continuous Deployment)"

                  Write-Host "##[section]Available Packages"
                  try {
                    # List available packages (requires Azure CLI or REST API call)
                    Write-Host "Feed: GovDigital/$(ARTIFACT_FEED)"
                    Write-Host "Package: gd-sql-obsd"
                    Write-Host "Requested Version: ${{ parameters.DeployVersion }}"
                  } catch {
                    Write-Host "Could not list packages: $($_.Exception.Message)"
                  }

            - task: PowerShell@2
              displayName: 'Update Pipeline MetaData'
              inputs:
                targetType: 'inline'
                script: |
                  Write-Host "##vso[build.updatebuildnumber]Build-$(Build.BuildId) [${{ parameters.Environment }}] Deploy $(parameters.DeployVersion)"

            - task: PowerShell@2
              displayName: 'Validate Version Parameter'
              inputs:
                targetType: 'inline'
                script: |
                  $deployVersion = "${{ parameters.DeployVersion }}"
                  Write-Host "Requested deployment version: $deployVersion"

                  if ([string]::IsNullOrWhiteSpace($deployVersion) -or $deployVersion -eq "latest") {
                    Write-Host "##vso[task.logissue type=error]Please specify a valid version to deploy (e.g., 1.0.0, 1.1.0, etc.)"
                    exit 1
                  }

                  # Validate version format (should be Major.Minor.Patch)
                  if (-not ($deployVersion -match '^\d+\.\d+\.\d+$')) {
                    Write-Host "##vso[task.logissue type=error]Version format should be Major.Minor.Patch (e.g., 1.0.0, 1.1.0, etc.)"
                    exit 1
                  }

                  Write-Host "Version validation passed: $deployVersion"

            - task: UniversalPackages@0
              displayName: 'Download Universal Package'
              retryCountOnTaskFailure: 3
              inputs:
                command: 'download'
                downloadDirectory: '$(Pipeline.Workspace)/downloaded-package'
                feedsToUse: 'internal'
                vstsFeed: 'GovDigital/$(ARTIFACT_FEED)'
                vstsFeedPackage: 'gd-sql-obsd'
                vstsPackageVersion: '${{ parameters.DeployVersion }}'
              env:
                ARTIFACT_FEED: $(ARTIFACT_FEED)
              continueOnError: false

            - task: PowerShell@2
              displayName: 'Verify Package Contents'
              inputs:
                targetType: 'inline'
                script: |
                  $packageDir = "$(Pipeline.Workspace)/downloaded-package"
                  $dacpacDir = "$packageDir/dacpac"
                  $dacpacFile = "$dacpacDir/ADFControlTablesDB.dacpac"

                  Write-Host "Package directory: $packageDir"
                  Write-Host "DACPAC directory: $dacpacDir"
                  Write-Host "DACPAC file: $dacpacFile"

                  # List package contents
                  Write-Host "Package contents:"
                  Get-ChildItem -Path $packageDir -Recurse | ForEach-Object {
                    Write-Host "  $($_.FullName)"
                  }

                  # Verify DACPAC file exists
                  if (-not (Test-Path $dacpacFile)) {
                    Write-Host "##vso[task.logissue type=error]DACPAC file not found: $dacpacFile"
                    exit 1
                  }

                  Write-Host "DACPAC file verified: $dacpacFile"

                  # Check if package manifest exists and display it
                  $manifestFile = "$packageDir/package-manifest.json"
                  if (Test-Path $manifestFile) {
                    Write-Host "Package manifest found:"
                    Get-Content $manifestFile | Write-Host
                  }

            - task: SqlAzureDacpacDeployment@1
              displayName: 'Execute Azure SQL : DacpacTask'
              inputs:
                azureSubscription: "$(SERVICE_CONNECTION)"
                ServerName: $(SQL_SERVER_FQDN)
                DatabaseName: $(SQL_DB_NAME)
                AuthenticationType: servicePrincipal
                DeploymentAction: 'Publish'
                DacpacFile: '$(Pipeline.Workspace)/downloaded-package/dacpac/ADFControlTablesDB.dacpac'
                SqlAdditionalArguments: '-b'