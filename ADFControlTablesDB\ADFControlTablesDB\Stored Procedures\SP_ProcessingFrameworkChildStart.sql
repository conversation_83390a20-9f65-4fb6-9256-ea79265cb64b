﻿CREATE PROCEDURE [Log].[SP_ProcessingFrameworkChildStart]
    @RunId NVARCHAR(MAX),
    @GroupId NVARCHAR(MAX),
    @Notebook NVARCHAR(MAX),
    @TableName NVARCHAR(255),
    @Stream NVARCHAR(MAX)
AS
BEGIN
    -- Insert a new record into the ProcessingFrameworkChildLog table
    INSERT INTO [Log].[ProcessingFrameworkChildLog] (RunId, GroupId, TableName, Notebook, Status, Message,
    NotebookRunUrl, StartTime, EndTime, Stream)
    VALUES (@RunId, @GroupId, @TableName, @Notebook, 'InProgress', NULL, NULL, CURRENT_TIMESTAMP, NULL, @Stream);
END;