# SQL Database CI/CD Pipeline

This repository contains the SQL database schema and automated CI/CD pipeline for the GD SQL OBSD (Government Digital SQL Operational Business Support Database) project.

## 📋 **Table of Contents**
- [Overview](#overview)
- [Quick Start](#quick-start)
- [Pipeline Architecture](#pipeline-architecture)
- [Usage Guide](#usage-guide)
- [Debugging & Troubleshooting](#debugging--troubleshooting)
- [Production Operations](#production-operations)
- [Maintenance](#maintenance)
- [Support](#support)

## 🎯 **Overview**

### **What This Pipeline Does**
- **CI (Continuous Integration)**: Automatically builds, versions, and publishes SQL database packages
- **CD (Continuous Deployment)**: Manually deploys specific versions to selected environments
- **Version Management**: Automatic semantic versioning with Git tags
- **Package Management**: Universal Packages for version control and distribution

### **Key Features**
- ✅ Automatic CI on push to main branch
- ✅ Manual CD with environment and version selection
- ✅ Semantic versioning (SemVer 2.0)
- ✅ Universal Packages integration
- ✅ Multi-environment support (DEV/UAT/PROD)
- ✅ Debug mode for troubleshooting
- ✅ Retry logic and error handling

## 🚀 **Quick Start**

### **CI (Automatic)**
1. Push code changes to `main` branch
2. Pipeline automatically triggers
3. Builds DACPAC, creates version tag, publishes package
4. **No deployment happens automatically**

### **CD (Manual)**
1. Go to Azure DevOps → Pipelines → sql-deployment
2. Click "Run pipeline"
3. Set parameters:
   - `RunMode`: **CD_Only**
   - `Environment`: **DEV/UAT/PROD**
   - `DeployVersion`: **1.0.0** (exact version)
   - `DebugMode`: **false** (true for troubleshooting)
4. Click "Run"

## 🏗️ **Pipeline Architecture**

### **File Structure**
```
.azurepipelines/
└── sql-deployment.yml    # Main CI/CD pipeline
```

### **CI Stage (Automatic)**
```mermaid
graph LR
    A[Push to Main] --> B[Build DACPAC]
    B --> C[Generate Version]
    C --> D[Create Git Tag]
    D --> E[Publish Package]
```

### **CD Stage (Manual)**
```mermaid
graph LR
    A[Manual Trigger] --> B[Validate Version]
    B --> C[Download Package]
    C --> D[Verify Contents]
    D --> E[Deploy to SQL]
```

## 📖 **Usage Guide**

### **Version Format**
- **Package Versions**: `1.0.0`, `1.1.0`, `2.0.0` (SemVer 2.0 format)
- **User Input**: `1.0.0` (when running CD pipeline)

### **Environment Configuration**
Each environment requires these variable groups:
- `DATA-PLATFORM-INFRA-{ENV}` (DEV/UAT/PROD)
- `DATA-PLATFORM-APP-{ENV}` (UAT/PROD only)

### **Required Variables**
```yaml
# In variable groups
SERVICE_CONNECTION: "Azure service connection name"
SQL_SERVER_FQDN: "sqlserver.database.windows.net"
SQL_DB_NAME: "DatabaseName"
ARTIFACT_FEED: "ArtifactFeedName"
AGENTPOOL: "AgentPoolName"
```

### **Workflow Examples**

#### **Development Workflow**
1. Developer makes changes to SQL schema
2. Commits and pushes to `main` branch
3. CI automatically runs → creates `v1.5` tag → publishes `1.5.0` package
4. When ready to deploy: Run CD with `Environment: DEV`, `Version: 1.5.0`

#### **Production Deployment**
1. Test deployment in UAT: `Environment: UAT`, `Version: 1.5.0`
2. Verify functionality and performance
3. Deploy to production: `Environment: PROD`, `Version: 1.5.0`

#### **Rollback Scenario**
1. Issue discovered in production
2. Run CD with previous version: `Environment: PROD`, `Version: 1.4.0`
3. Database rolls back to previous schema

## 🔍 **Debugging & Troubleshooting**

### **Enable Debug Mode**
Set `DebugMode: true` when running the pipeline to get:
- Detailed environment information
- Variable values and validation
- Package availability checks
- Extended logging for diagnosis

### **Common Issues & Solutions**

#### **❌ Variable Group Authorization Error**
```
Variable group was not found or is not authorized for use
```
**Solution:**
1. Go to Azure DevOps → Pipelines → Library
2. Find variable groups: `DATA-PLATFORM-INFRA-{ENV}`, `DATA-PLATFORM-APP-{ENV}`
3. Click Security tab → Add pipeline permissions

#### **❌ Package Version Format Error**
```
Universal package versions must be lowercase SemVer 2.0 versions
```
**Solution:**
- ✅ Use: `1.0.0`, `1.1.0`, `2.0.0`
- ❌ Don't use: `1.0`, `v1.0`, `1.0-beta`

#### **❌ Package Not Found**
```
Package version provided is invalid
```
**Solution:**
1. Check Azure DevOps → Artifacts → Feeds → GovDigital/{FEED_NAME}
2. Verify package `gd-sql-obsd` exists
3. Confirm version exists (run CI first if needed)

#### **❌ SQL Connection Issues**
```
Cannot connect to SQL Server
```
**Solution:**
1. Verify `$(SERVICE_CONNECTION)` has valid credentials
2. Check `$(SQL_SERVER_FQDN)` and `$(SQL_DB_NAME)` values
3. Validate service principal has database permissions

### **Debug Commands**

#### **Check Pipeline Variables**
Enable `DebugMode: true` and look for these in logs:
```
SERVICE_CONNECTION: [value]
SQL_SERVER_FQDN: [value]
SQL_DB_NAME: [value]
ARTIFACT_FEED: [value]
```

#### **Verify Package Availability**
1. Go to Azure DevOps → Artifacts
2. Navigate to feed: `GovDigital/{ARTIFACT_FEED}`
3. Find package: `gd-sql-obsd`
4. Check available versions

---

**For additional support or questions, please refer to the [Wiki page](https://dev.azure.com/GOVTALENT/GTDATA/_wiki/wikis/GTDATA.wiki/151/DGE-DATA-Solution-Deployment-workflow) or contact the DevOps team.**