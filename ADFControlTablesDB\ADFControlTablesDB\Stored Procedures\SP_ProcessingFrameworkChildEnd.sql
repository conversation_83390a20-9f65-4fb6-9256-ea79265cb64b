﻿CREATE PROCEDURE [Log].[SP_ProcessingFrameworkChildEnd]
    @RunId NVARCHAR(MAX),
    @GroupId NVARCHAR(MAX),
    @Notebook NVARCHAR(MAX),
    @Status NVARCHAR(100),
    @Message NVARCHAR(MAX),
    @NotebookRunUrl NVARCHAR(MAX),
    @EndTime NVARCHAR(150)
AS
BEGIN
    -- Update the record for the given RunId&ParentId&Notebook with given status, current EndTime, and message if any.
    UPDATE [Log].[ProcessingFrameworkChildLog]
    SET Status = @Status,
        Message = @Message,
        NotebookRunUrl = @NotebookRunUrl,
        EndTime = CAST(@EndTime AS DATETIME)  
    WHERE RunId = @RunId
      AND GroupId = @GroupId
      AND Notebook = @Notebook
      AND Status = 'InProgress'; -- Optional: Ensure it only updates in-progress entries
END;