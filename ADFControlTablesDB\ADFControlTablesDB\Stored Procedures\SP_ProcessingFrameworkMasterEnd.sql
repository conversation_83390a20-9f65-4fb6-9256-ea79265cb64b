﻿CREATE PROCEDURE [Log].[SP_ProcessingFrameworkMasterEnd]
    @RunId NVARCHAR(MAX),
    @GroupId NVARCHAR(MAX),
    @Status NVARCHAR(100)
AS
BEGIN
    -- Update the record for the given RunId with given status and current EndTime
    UPDATE [Log].[ProcessingFrameworkMasterLog]
    SET Status = @Status,
        EndTime = CURRENT_TIMESTAMP
    WHERE RunId = @RunId
      AND GroupId = @GroupId
      AND Status = 'InProgress'; -- Optional: Ensure it only updates in-progress entries
END;