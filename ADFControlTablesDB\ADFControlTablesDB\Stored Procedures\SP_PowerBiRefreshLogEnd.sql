﻿CREATE PROCEDURE [Log].[SP_PowerBiRefreshLogEnd]
    @RunId 					NVARCHAR(MAX),
	@DatasetId				NVARCHAR(MAX),
	@WorkspaceId			NVARCHAR(MAX),
	@Status					NVARCHAR(MAX),
	@MessageError			NVARCHAR(MAX)
AS
BEGIN
    -- Insert a new record into the PowerBiRefreshLog table
    UPDATE [Log].[PowerBiRefreshLog] 
	SET [Status] = @Status,
		EndTime = CURRENT_TIMESTAMP,
		MessageError = @MessageError 
    WHERE RunId = @RunId
		AND WorkspaceId = @WorkspaceId
		AND [Status] = 'InProgress';
END;