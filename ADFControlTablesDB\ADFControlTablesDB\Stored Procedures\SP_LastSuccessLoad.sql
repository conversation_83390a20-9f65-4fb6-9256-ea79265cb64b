﻿CREATE PROCEDURE [Control].[SP_LastSuccessLoad]
    @TableName NVARCHAR(MAX),
    @LastSuccesLoad  NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;

    MERGE INTO [Control].[LastSuccessLoad] AS target
    USING (SELECT @TableName AS TableName) AS source
    ON target.TableName = source.TableName
    WHEN MATCHED THEN 
        UPDATE SET target.LastSuccessLoad = CAST(@LastSuccesLoad AS DATETIME) 
    WHEN NOT MATCHED THEN
        INSERT (TableName, LastSuccessLoad)
        VALUES (@TableName, CAST(@LastSuccesLoad AS DATETIME));
END;