﻿CREATE PROCEDURE [Log].[SP_ProcessingFrameworkMasterStart]
    @RunId NVARCHAR(MAX),
    @GroupId NVARCHAR(MAX),
    @TriggerName NVARCHAR(MAX),
    @Stream NVARCHAR(MAX)
AS
BEGIN
    -- Insert a new record into the ProcessingFrameworkMasterLog table
    INSERT INTO [Log].[ProcessingFrameworkMasterLog] (RunId, GroupId, Status, StartTime, EndTime, TriggerName, Stream)
    VALUES (@RunId, @GroupId, 'InProgress', CURRENT_TIMESTAMP, NULL, @TriggerName, @Stream);
END;