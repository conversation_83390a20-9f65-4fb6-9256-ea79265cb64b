﻿CREATE PROCEDURE [Log].[SP_PowerBiRefreshLogDataset]
	@RunId 						NVARCHAR(MAX),
	@WorkspaceId 				NVARCHAR(MAX),
    @RefreshType 				NVARCHAR(MAX),
	@StartTime					NVARCHAR(MAX),
	@EndTime					NVARCHAR(MAX),
	@Status						NVARCHAR(MAX),
	@ServiceExeptionJson		NVARCHAR(MAX)
AS
BEGIN
    -- Insert a new record into the PowerBiRefreshLog table
    UPDATE [Log].[PowerBiRefreshLog] 
	SET RefreshType = @RefreshType,
		DatasetStartTime = CAST(@StartTime AS DATETIME),
		DatasetEndTime = CAST(@EndTime AS DATETIME),
		DatasetStatus = @Status,
		DatasetError = @ServiceExeptionJson
    WHERE RunId = @RunId
		AND WorkspaceId = @WorkspaceId;
END;